<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/apuestatipo.php';
require_once __ROOT__ . '/src/classes/partidoporrevisar.php';
require_once __ROOT__ . '/src/classes/partidoinfo.php';
require_once __ROOT__ . '/src/classes/partidoapuesta.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/classes/paisoculto.php';
require_once __ROOT__ . '/src/general/preparar.php';

$idpartidoporrevisar = '';
$matchup = '';
$newpartido = new Partido();
$newpartido->fecha = create_date();
$fechadiashora = '';
$sum_riesgo = 0;
$fontsizeicons = "fs-15px";
$horamilitar = '';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

        if (isset($_SESSION['idpartidoporrevisar'])) {
            $idpartidoporrevisar = $_SESSION['idpartidoporrevisar'];
        
            // logic:
            $modpartidoporrevisar = PartidoPorRevisar::get($idpartidoporrevisar, $conexion);

            $newpartido->pais        = $modpartidoporrevisar->pais;
            $newpartido->fecha       = $modpartidoporrevisar->fecha;
            $newpartido->horamilitar = $modpartidoporrevisar->hora;
            $newpartido->formhome    = $modpartidoporrevisar->formhome;
            $newpartido->formaway    = $modpartidoporrevisar->formaway;
            $horamilitar             = $modpartidoporrevisar->horamilitar;
            $newpartido->home_xg     = $modpartidoporrevisar->home_xg;
            $newpartido->away_xg     = $modpartidoporrevisar->away_xg;
            $newpartido->home        = $modpartidoporrevisar->equipo_home;
            $newpartido->away        = $modpartidoporrevisar->equipo_away;
        
            unset($_SESSION['idpartidoporrevisar']);
        }
        if (isset($_SESSION['loadmatchup'])){
            $equipos = explode(" vs ", $_SESSION['matchup']);

            $newpartido->home = $equipos[0];
            $newpartido->away = $equipos[1];

            unset($_SESSION['loadmatchup']);
            unset($_SESSION['matchup']);
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $method_postsolo = 1;
        
        $idpartidoporrevisar = limpiar_datos($_POST['idpartidoporrevisar']);
        $horamilitar         = limpiar_datos($_POST['horamilitar']);
        
        $newpartido->home        = $_POST['home'];
        $newpartido->away        = $_POST['away'];
        $newpartido->pais        = $_POST['pais'];
        $newpartido->fecha       = limpiar_datos($_POST['fecha']);
        $newpartido->hora        = limpiar_datos($_POST['hora']);
        $newpartido->formhome    = limpiar_datos($_POST['formhome']);
        $newpartido->formaway    = limpiar_datos($_POST['formaway']);
        $newpartido->horamilitar = limpiar_datos($_POST['horamilitar']);
        $newpartido->home_xg     = limpiar_datos($_POST['home_xg']);
        $newpartido->away_xg     = limpiar_datos($_POST['away_xg']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && (isset($_POST['sub_add']) || isset($_POST['sub_add_next']))) {
    try {
        $conexion->beginTransaction();
        
        //crear partido
        $newpartido->add($conexion);

        //marcar partido por revisar como revisado.
        if(!empty($idpartidoporrevisar)){
            $modpartidoporrevisar     = new PartidoPorRevisar;
            $modpartidoporrevisar->id = $idpartidoporrevisar;
            $modpartidoporrevisar->modifyDone($conexion);
        }

        $conexion->commit();

        $_SESSION['idpartido'] = $newpartido->id;

        if(isset($_POST['sub_add'])){
            header('Location: ipartido_odds');
            exit();
        }
        if(isset($_POST['sub_add_next'])){
            ir_proximo_partido_porrevisar($conexion);
            exit();
        }
    } catch (Exception $e) {
        $conexion->rollback();
    
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add
#region sub_delpartidoporrevisar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delpartidoporrevisar'])) {
    try {
        $method_sub_delpartidoporrevisar = 1;

        if(!empty($idpartidoporrevisar)){
            PartidoPorRevisar::delete($idpartidoporrevisar, $conexion);

            ir_proximo_partido_porrevisar($conexion);
            exit();

        } else{
            throw new Exception('No es posible eliminar este partido. Esta opcion solo es para partidos por revisar.');
        }

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delpartidoporrevisar
#region sub_del_all_partidos_torneo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_del_all_partidos_torneo'])) {
    try {
        PartidoPorRevisar::delete_all_same_torneo($newpartido->pais, $conexion);

        ir_proximo_partido_porrevisar($conexion);
        exit();

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_del_all_partidos_torneo
#region sub_ocultarpais
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_ocultarpais'])) {
    try {
        $method_sub_ocultarpais = 1;
        
        $newpaisoculto = new PaisOculto;
        $newpaisoculto->nombre = $newpartido->pais;
        $newpaisoculto->add($conexion);

        //Al ocultar torneo ir al proximo por revisar.
        $respuesta = PartidoPorRevisar::getIdProximoPorRevisar($conexion);
        $_SESSION['idpartidoporrevisar'] = $respuesta['id'];

        if(($respuesta['count'] > 0)){
            header('Location: ipartido');
            exit();
        } else{
            header('Location: lpartidosporrevisar?d=1');
            exit();
        }

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_ocultarpais
#region try
try {
    $method_try = 1;
    
    $apuestastipos            = ApuestaTipo::getList(array(),$conexion);
    $paises                   = Pais::getList($conexion);
    $teams                    = PartidoInfo::getListTeams($conexion);
    $respuesta                = PartidoPorRevisar::getIdProximoPorRevisar($conexion);
    $count_partidosporrevisar = $respuesta['count'];

    $param               = array();
    $param['solotoplay'] = 1;
    $param['solo_notdone'] = 1;
    $param['nom_pais']   = $newpartido->pais;
    $count_partidos_mismotorneo_porrevisar = count(PartidoPorRevisar::getList($param, $conexion));

    if(!empty(Pais::getByNombre($newpartido->pais, $conexion))){
        $paiscreado = 1;
    } else{
        $paiscreado = 0;
    }
    if(PartidoInfo::getListCountByPais($newpartido->pais, $conexion) > 0){
        $infocargada = 1;
    } else{
        $infocargada = 0;
    }
    
} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

/**
 * @throws Exception
 */
function ir_proximo_partido_porrevisar(PDO $conexion): void
{
    //Al eliminar un partido ir al proximo por revisar.
    $respuesta = PartidoPorRevisar::getIdProximoPorRevisar($conexion);
    $_SESSION['idpartidoporrevisar'] = $respuesta['id'];

    if(!empty($_SESSION['idpartidoporrevisar'])){
        header('Location: ipartido');
    } else{
        header('Location: lpartidosporrevisar?d=1');
    }
}

require_once __ROOT__ . '/views/ipartido.view.php';

?>

